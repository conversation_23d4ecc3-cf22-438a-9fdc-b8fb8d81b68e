import { formRequest } from '../request';

type requestRs = {
  data: unknown;
  code: number;
  msg: string;
};
// 获取字典子节点数据
const getDict: (parentCode: string) => Promise<requestRs['data']> = async (parentCode: string) => {
  const resp: requestRs = await formRequest.get(`/getDic/${parentCode}`);

  const { data = [] } = resp ?? {};

  return data;
};
// 化工园区项目获取字典子节点数据
const getDictionary: (params?: Record<string, string>) => Promise<requestRs['data']> = async (
  params = {},
) => {
  const resp: requestRs = await formRequest.post('/dataDictionary/query', {
    data: params,
  });

  const { data = {} } = resp ?? {};

  return data;
};

// 获取字典树形数据
const getTreeDict: (parentCode: string) => Promise<requestRs['data']> = async (
  parentCode: string,
) => {
  const resp: requestRs = await formRequest.get(`/getTreeDic/${parentCode}`);

  const { data = [] } = resp ?? {};

  return data;
};

/** 获取字典子节点数据 */
const getDictData: (code?: string) => Promise<requestRs['data']> = async (code?: string) =>
  !code ? [] : ((await formRequest.get(`/V2/api/getDic/${code}`))?.data ?? []);

export default { getDict, getTreeDict, getDictionary, getDictData };
