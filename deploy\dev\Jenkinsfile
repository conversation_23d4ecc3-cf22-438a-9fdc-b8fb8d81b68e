pipeline {
    agent any
    environment {
        // harbor 凭证
        DOCKER_CREDENTIAL_ID = 'harbor'
        // harbor 地址
        REGISTRY = 'yth.harbor.com:9443'
        // k8s 命名空间
        KUBECONFIG_NAMESPACE = 'campus'
        // 镜像版本
        BUILD_TIMESTAMP = sh(returnStdout: true, script: 'date +%Y%m%d%H%M%S').trim()
    }

    stages {
        stage('拉取代码并修改配置文件') {
            steps {
                git branch: "${branch}",
                credentialsId: 'credentials-118',
                url: 'http://**********:3000/CIP/module-inspection.git'
            }
        }

        stage('编译打包并上传镜像') {
            steps {
                sh 'git log -1 --pretty=format:"%h" > ./gitversion'
                script {
                    env.GITVERSION = sh(returnStdout: true, script: 'cat ./gitversion').trim()
                }
                // 编译打包
                sh 'rm -rf build'
                sh 'rm -rf dist'
                sh 'npm config set registry http://*********:4873/repository/npm-group/'
                sh 'node -v'
                sh 'npm install --legacy-peer-deps'
                sh 'npm install yth-ui'
                sh 'npm run build'

                script {
                    if (env.active == 'prod') {
                        env.CUSTOM_NAMESPACE = env.HARBOR_NAMESPACE
                    } else {
                        env.CUSTOM_NAMESPACE = 'campus'
                    }
                    echo "CUSTOM_NAMESPACE: ${env.CUSTOM_NAMESPACE}"
                }

                // 打docker镜像
                sh 'docker build -f `pwd`/deploy/Dockerfile -t $REGISTRY/$CUSTOM_NAMESPACE/$active/campus-front-inspection:v$GITVERSION-$BUILD_TIMESTAMP .'

                // 登录harbor 并上传docker镜像
                withCredentials([usernamePassword(passwordVariable : 'DOCKER_PASSWORD' ,usernameVariable : 'DOCKER_USERNAME' ,credentialsId : "$DOCKER_CREDENTIAL_ID" ,)]) {
                    sh 'echo "$DOCKER_PASSWORD" | docker login $REGISTRY -u "$DOCKER_USERNAME" --password-stdin'
                    sh 'docker push  $REGISTRY/$CUSTOM_NAMESPACE/$active/campus-front-inspection:v$GITVERSION-$BUILD_TIMESTAMP'
                }
            }
        }

        stage('镜像打tag') {
            steps {
                // 给镜像打上tag
                sh 'docker tag $REGISTRY/$CUSTOM_NAMESPACE/$active/campus-front-inspection:v$GITVERSION-$BUILD_TIMESTAMP $REGISTRY/$CUSTOM_NAMESPACE/$active/campus-front-inspection:latest'
                sh 'docker push $REGISTRY/$CUSTOM_NAMESPACE/$active/campus-front-inspection:latest'
            }
        }

        stage('部署到云') {
          steps {
              script{
                  if(env.active=='dev'){
                      kubernetesDeploy(enableConfigSubstitution: true, configs: "deploy/dev/*.yaml",  kubeconfigId: "testkubeconfig")
                  }else{
                      sh 'echo campus-front-inspection:v$GITVERSION-$BUILD_TIMESTAMP images build success'
                  }
              }
          }
        }
    }
}
