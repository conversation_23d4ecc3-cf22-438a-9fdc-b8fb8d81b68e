import React, { useState, useRef } from 'react';
import { YTHList, YTHLocalization, YTHDialog } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button, message, Space } from 'antd';
import locales from '@/locales';
import dicParams from '@/pages/inspection/common/util/dicParams';
import moment from 'moment';
import { DownloadOutlined } from '@ant-design/icons';
import PlanApi from '@/service/inspection/planApi';
import {
  InspectionPlanVo,
  PlanPageQueryParam,
  PlanPageResponse,
  PlanQueryParam,
} from '@/types/inspection/plan';
import formApi from '@/service/formApi';
import AddDialog from './addDialog';

/**
 * @description 运输管理 运输公司信息管理
 */
const LogisticsCompanyInfoList: React.FC = () => {
  const ref: React.MutableRefObject<ActionType | undefined> = useRef<ActionType>();
  const aa: ActionType = YTHList.createAction();
  const [modalType, setModalType] = useState<string>(''); // 是否是新增模式
  const [dataObj, setDataObj] = useState<{ [key: string]: React.Key }>({}); // 查看或编辑行数据
  const [editMenuVisiable, setEditMenuVisiable] = useState<boolean>(false); // 标记弹窗是否显示

  const [queryData, setQueryData] = useState<PlanPageQueryParam>(); // 查询参数 用于导出
  const [listDataTotal, setListDataTotal] = useState<number>(0);

  // 导出数据
  const exportByPage: () => Promise<void> = async () => {
    if (listDataTotal === 0) {
      message.warn('无可导出数据！');
      return;
    }
    if (listDataTotal > 10000) {
      message.warn('当前查询结果超过10000条，仅导出前10000条数据');
    }
    const params: PlanPageQueryParam = {
      ...queryData,
      pageSize: 10000,
      currentPage: 1,
    };
    try {
      await PlanApi.exportByPage(params);
    } catch (error) {
      message.error(error);
    }
  };

  const columns: IYTHColumnProps[] = [
    { dataIndex: 'serialNo', title: '序号', width: 80, display: false },
    {
      dataIndex: 'planName',
      title: '计划名称',
      query: true,
      display: true,
      componentProps: {
        placeholder: '名称关键词',
      },
    },
    { dataIndex: 'planTypeText', title: '计划类型', query: false, display: true },
    { dataIndex: 'inspectionMethodText', title: '巡检方式', width: 100, query: false },
    {
      dataIndex: 'inspectionMethodData',
      title: '巡检方式',
      width: 0,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          allowClear: true,
          placeholder: '选择巡检方式',
        },
        request: async () => {
          return (await formApi.getDictData(dicParams.INSPECTION_METHOD)) ?? [];
        },
      },
      render: (_, record) => record?.typeText ?? '-',
    },
    {
      dataIndex: 'executionFrequencyText',
      title: '执行频率',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'frequencyNumber',
      title: '执行频率',
      width: 100,
      query: false,
      display: true,
      sorter: true,
    },
    {
      dataIndex: 'validStartDate',
      title: '有效开始时间',
      width: 0,
      queryMode: 'group',

      display: false,
      query: true,
      componentName: 'DatePicker',
      defaultValue: [
        moment().subtract(1, 'days').format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD'),
      ],
      componentProps: {
        placeholder: '请输入',
        precision: `day`,
        formatter: `YYYY-MM-DD`,
      },
    },
    { dataIndex: 'directorUserName', title: '负责人', width: 90, query: true, display: true },
    { dataIndex: 'directorPhone', title: '联系电话', width: 100, query: true, display: true },
    { dataIndex: 'taskStartDate', title: '任务开始时间', width: 90, query: false, display: true },
  ];
  // 设置弹窗标题
  const setModalTitle: () => string = () => {
    let title: string = '';
    if (modalType === 'add') {
      title = '新增';
    } else if (modalType === 'view') {
      title = '查看';
    } else if (modalType === 'edit') {
      title = '编辑';
    }
    return title;
  };

  // 关闭弹窗
  const closeModal: () => void = () => {
    setEditMenuVisiable(false);
    aa.reload({});
    Modal.destroyAll();
  };
  const confirmDelete: (row: InspectionPlanVo) => Promise<void> = async (row) => {
    const res: { code?: number; msg?: string } = await PlanApi.deleteById(row.id);
    if (res && res.code && res.code === 200) {
      message.success('删除数据成功');
      aa.reload({});
    } else {
      message.error('删除数据出错');
    }
  };
  const deleteTemplateDialog: (row: InspectionPlanVo) => Promise<void> = async (row) => {
    YTHDialog.show({
      type: 'confirm',
      content: <p>确认删除此条数据？</p>,
      onCancle: () => {},
      onConfirm: () => {
        confirmDelete(row);
      },
      p_props: {
        cancelText: '取消',
        okText: '确定',
        title: '删除',
      },
      m_props: {
        title: '删除',
      },
    });
  };

  const handleFilter: (f: PlanQueryParam) => PlanQueryParam = (f: PlanQueryParam) => {
    const filter: PlanQueryParam = f || {};
    if (f.planName && f.planName !== '') {
      filter.planName = f.planName;
    }
    return filter;
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="envQualityOnLineMonitorList"
        action={aa}
        actionRef={ref}
        showRowSelection={false}
        extraOperation={[
          {
            element: (
              <div>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  size="small"
                  onClick={exportByPage}
                >
                  {' '}
                  导出{' '}
                </Button>
              </div>
            ),
          },
        ]}
        operation={[
          {
            element: (
              <div>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    setEditMenuVisiable(true);
                    setModalType('add');
                  }}
                >
                  {' '}
                  新增{' '}
                </Button>
              </div>
            ),
          },
        ]}
        listKey="id"
        request={async (filter, pagination, sort) => {
          try {
            const convertFieldName: (field: string) => string = (field: string) =>
              field.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();

            let descColumns: string = 'create_date';
            let ascColumns: string = '';

            if (sort?.order && sort.field) {
              const convertedField: string = convertFieldName(sort.field);
              if (sort.order === 'desc') {
                descColumns = convertedField;
              } else if (sort.order === 'asc') {
                ascColumns = convertedField;
              }
            }
            const queryParams: PlanPageQueryParam = {
              descs: [descColumns],
              aescs: [ascColumns],
              condition: handleFilter(filter),
              currentPage: pagination.current,
              pageSize: pagination.pageSize,
            };
            setQueryData(queryParams);
            const resData: PlanPageResponse = await PlanApi.queryByPage(queryParams);
            if (resData.code && resData.code === 200) {
              const dataWithSerialNo: (InspectionPlanVo & { serialNo: number })[] =
                resData.data.map((item: InspectionPlanVo, index: number) => ({
                  ...item,
                  serialNo: (pagination.current - 1) * pagination.pageSize + index + 1,
                }));
              setListDataTotal(resData.total);
              return {
                data: dataWithSerialNo,
                total: resData.total,
                success: true,
              };
            }
            message.error('请求数据出错，请刷新重试或联系管理员');
            setListDataTotal(0);
            return {
              data: [],
              total: 0,
              success: false,
            };
          } catch {
            message.error('请求数据出错，请刷新重试或联系管理员');
            setListDataTotal(0);
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        }}
        rowOperationWidth={200}
        // 资质状态标志，1：表示证件状态正常 0：表示证件已过期 2：表示证件即将过期
        rowProps={() => ({})}
        rowOperation={(row) => {
          return [
            {
              element: (
                <div className="gasLeakMonitor-row-operator">
                  <Space size="middle">
                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        setModalType('view');
                        setDataObj(row);
                        setEditMenuVisiable(true);
                      }}
                    >
                      查看
                    </Button>

                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        setModalType('edit');
                        setDataObj(row);
                        setEditMenuVisiable(true);
                      }}
                    >
                      编辑
                    </Button>

                    <Button
                      size="small"
                      type="link"
                      danger
                      onClick={() => {
                        deleteTemplateDialog(row as InspectionPlanVo);
                      }}
                    >
                      删除
                    </Button>
                  </Space>
                </div>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        width="60%"
        title={setModalTitle()}
        footer={null}
        visible={editMenuVisiable}
        onCancel={closeModal}
        destroyOnClose
        maskClosable={false}
      >
        <AddDialog type={modalType} closeModal={closeModal} dataObj={dataObj} />
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(
  LogisticsCompanyInfoList,
  locales['zh-CN'],
  YTHLocalization.getLanguage(),
);
