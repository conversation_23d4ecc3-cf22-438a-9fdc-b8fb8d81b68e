import { logisticsRequest } from '@/request';
import downloadBlob from '@/service/downloadBlob';
import { BaseResponse } from '@/types/common';

import {
  InspectionPlanVo,
  PlanInsertParam,
  PlanPageQueryParam,
  PlanPageResponse,
  PlanUpdateParam,
} from '@/types/inspection/plan';

export default {
  /**
   * @param data InsertParam 待新增的计划信息
   * @returns 新增结果 || null
   * @description 新增
   */
  insert: async (data: PlanInsertParam) => {
    return logisticsRequest.post<BaseResponse<object>>(`/inspectionPlan/insert`, { data });
  },
  /**
   * @param data updateParam 待更新的计划信息
   * @returns 更新结果 || null
   * @description 修改
   */
  update: async (data: PlanUpdateParam) => {
    return logisticsRequest.put<BaseResponse<object>>(`/inspectionPlan/update`, { data });
  },
  /**
   * @param id 计划id
   * @returns 删除结果 || null
   * @description deleteById
   */
  deleteById: async (id: string) => {
    return logisticsRequest.delete<BaseResponse<object>>(`/inspectionPlan/deleteById`, {
      params: { id },
    });
  },
  /**
   * @param data {id:''} 计划id
   * @returns 查询结果 || null
   * @description 根据id查询计划信息
   */
  getDetailById: async (data: { id: string }) => {
    return logisticsRequest.get<BaseResponse<InspectionPlanVo>>(`/inspectionPlan/getById`, {
      params: data,
    });
  },
  /**
   * @param data PageQueryParam 分页查询参数
   * @returns  计划信息列表 || null
   * @description 获取列表
   */
  queryByPage: async (data: PlanPageQueryParam) => {
    return logisticsRequest.post<PlanPageResponse>(`/inspectionPlan/queryByPage`, { data });
  },
  /**
   * @param data PageQueryParam 分页查询参数
   * @returns  计划信息导出文件 || null
   * @description 导出列表
   */
  exportByPage: async (data: PlanPageQueryParam) => {
    return new Promise((resolve) => {
      downloadBlob('/inspectionPlan/exportByPage', {
        method: 'POST',
        data,
      }).finally(() => {
        resolve('finally');
      });
    });
  },
};
